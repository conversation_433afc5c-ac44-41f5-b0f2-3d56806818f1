Shader "Custom/GrassBladesMobile"
{
    Properties
    {
        [Header(Grass Colors)]
        _TopTint("Top Tint", Color) = (0.8, 1.0, 0.6, 1.0)
        _BottomTint("Bottom Tint", Color) = (0.2, 0.6, 0.2, 1.0)
        
        [Header(Fade Settings)]
        _Fade("Top Fade Offset", Range(-1, 10)) = 1.0
        _Stretch("Top Fade Stretch", Range(0.1, 10)) = 2.0
        
        [Header(Terrain Blending)]
        [Toggle(BLEND_TERRAIN)] _BlendTerrain("Blend with Terrain", Float) = 0
        _TerrainDiffuse("Terrain Texture", 2D) = "white" {}
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType" = "Opaque" 
            "Queue" = "Geometry"
            "RenderPipeline" = "UniversalPipeline"
        }
        
        LOD 100
        Cull Off
        ZWrite On
        
        Pass
        {
            Name "ForwardLit"
            Tags { "LightMode" = "UniversalForward" }
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
            #pragma shader_feature_local BLEND_TERRAIN
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            
            // Simplified data structures
            struct DrawVertex
            {
                float3 positionWS;
                float2 uv;
            };
            
            struct DrawTriangle
            {
                float3 normalOS;
                float3 diffuseColor;
                float4 extraBuffer;
                DrawVertex vertices[3];
            };
            
            #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                StructuredBuffer<DrawTriangle> _DrawTriangles;
            #endif
            
            CBUFFER_START(UnityPerMaterial)
                float4 _TopTint;
                float4 _BottomTint;
                float _Fade;
                float _Stretch;
                float _OrthographicCamSizeTerrain;
                float3 _OrthographicCamPosTerrain;
            CBUFFER_END
            
            #ifdef BLEND_TERRAIN
                TEXTURE2D(_TerrainDiffuse);
                SAMPLER(sampler_TerrainDiffuse);
            #endif
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                uint vertexID : SV_VertexID;
            };
            
            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float3 positionWS : TEXCOORD0;
                float3 normalWS : TEXCOORD1;
                float2 uv : TEXCOORD2;
                float3 diffuseColor : TEXCOORD3;
                float4 extraBuffer : TEXCOORD4;
            };
            
            Varyings vert(Attributes input)
            {
                Varyings output = (Varyings)0;
                
                #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                    DrawTriangle tri = _DrawTriangles[input.vertexID / 3];
                    DrawVertex vertex = tri.vertices[input.vertexID % 3];
                    
                    output.positionWS = vertex.positionWS;
                    output.positionCS = TransformWorldToHClip(vertex.positionWS);
                    output.normalWS = TransformObjectToWorldNormal(tri.normalOS);
                    output.uv = vertex.uv;
                    output.diffuseColor = tri.diffuseColor;
                    output.extraBuffer = tri.extraBuffer;
                    
                    if (tri.extraBuffer.x == -1)
                    {
                        output.extraBuffer.x = 99999;
                    }
                #else
                    output.positionWS = TransformObjectToWorld(input.positionOS.xyz);
                    output.positionCS = TransformWorldToHClip(output.positionWS);
                    output.normalWS = float3(0, 1, 0);
                    output.uv = float2(0.5, 0.5);
                    output.diffuseColor = float3(0.5, 0.8, 0.3);
                    output.extraBuffer = float4(99999, 0, 0, 0);
                #endif
                
                return output;
            }
            
            half4 frag(Varyings input) : SV_Target
            {
                // Cut grass clipping
                float cutoffTop = step(input.positionWS.y, input.extraBuffer.x);
                clip(cutoffTop - 0.01);
                
                // Simple vertical fade
                float verticalFade = saturate((input.uv.y + _Fade) * _Stretch);
                
                // Base color
                float4 baseColor = lerp(_BottomTint, _TopTint, verticalFade);
                baseColor *= float4(input.diffuseColor, 1.0);
                
                // Terrain blending
                #ifdef BLEND_TERRAIN
                    float2 terrainUV = input.positionWS.xz - _OrthographicCamPosTerrain.xz;
                    terrainUV = terrainUV / (_OrthographicCamSizeTerrain * 2.0);
                    terrainUV += 0.5;
                    
                    float4 terrainColor = SAMPLE_TEXTURE2D(_TerrainDiffuse, sampler_TerrainDiffuse, terrainUV);
                    baseColor = lerp(terrainColor, baseColor, verticalFade * 0.8);
                #endif
                
                // Simple lighting
                Light mainLight = GetMainLight();
                float3 lightDir = mainLight.direction;
                float3 normalWS = normalize(input.normalWS);
                float NdotL = saturate(dot(normalWS, lightDir));
                
                float3 finalColor = baseColor.rgb * mainLight.color * (NdotL * 0.7 + 0.3);
                
                return half4(finalColor, 1.0);
            }
            
            ENDHLSL
        }

        // Shadow caster pass for mobile
        Pass
        {
            Name "ShadowCaster"
            Tags { "LightMode" = "ShadowCaster" }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull Off

            HLSLPROGRAM
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment

            #pragma multi_compile_instancing
            #pragma multi_compile _ _CASTING_PUNCTUAL_LIGHT_SHADOW

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/CommonMaterial.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/ShadowCasterPass.hlsl"

            struct DrawVertex
            {
                float3 positionWS;
                float2 uv;
            };

            struct DrawTriangle
            {
                float3 normalOS;
                float3 diffuseColor;
                float4 extraBuffer;
                DrawVertex vertices[3];
            };

            #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                StructuredBuffer<DrawTriangle> _DrawTriangles;
            #endif

            struct ShadowAttributes
            {
                float4 positionOS : POSITION;
                uint vertexID : SV_VertexID;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct ShadowVaryings
            {
                float4 positionCS : SV_POSITION;
                float3 positionWS : TEXCOORD0;
                float4 extraBuffer : TEXCOORD1;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            float3 _LightDirection;
            float3 _LightPosition;

            ShadowVaryings ShadowPassVertex(ShadowAttributes input)
            {
                ShadowVaryings output = (ShadowVaryings)0;

                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);

                #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                    DrawTriangle tri = _DrawTriangles[input.vertexID / 3];
                    DrawVertex vertex = tri.vertices[input.vertexID % 3];

                    output.positionWS = vertex.positionWS;
                    output.extraBuffer = tri.extraBuffer;

                    if (tri.extraBuffer.x == -1)
                    {
                        output.extraBuffer.x = 99999;
                    }

                    float3 normalWS = TransformObjectToWorldNormal(tri.normalOS);

                    #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                        float3 lightDirectionWS = normalize(_LightPosition - vertex.positionWS);
                    #else
                        float3 lightDirectionWS = _LightDirection;
                    #endif

                    output.positionCS = TransformWorldToHClip(ApplyShadowBias(vertex.positionWS, normalWS, lightDirectionWS));
                #else
                    float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
                    output.positionWS = positionWS;
                    output.extraBuffer = float4(99999, 0, 0, 0);
                    output.positionCS = TransformWorldToHClip(positionWS);
                #endif

                return output;
            }

            half4 ShadowPassFragment(ShadowVaryings input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input);

                // Cut grass clipping
                float cutoffTop = step(input.positionWS.y, input.extraBuffer.x);
                clip(cutoffTop - 0.01);

                return 0;
            }

            ENDHLSL
        }
    }

    Fallback "Hidden/Universal Render Pipeline/FallbackError"
}
