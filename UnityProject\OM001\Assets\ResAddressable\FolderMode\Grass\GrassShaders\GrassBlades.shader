Shader "Custom/GrassBlades"
{
    Properties
    {
        [Header(Grass Colors)]
        _TopTint("Top Tint", Color) = (0.8, 1.0, 0.6, 1.0)
        _BottomTint("Bottom Tint", Color) = (0.2, 0.6, 0.2, 1.0)
        _AmbientAdjustmentColor("Ambient Adjustment", Color) = (0.5, 0.5, 0.5, 1.0)
        
        [Header(Material Properties)]
        _Metallic("Metallic", Range(0, 1)) = 0.0
        _Smoothness("Smoothness", Range(0, 1)) = 0.3
        _Subsurface("Subsurface", Range(0, 1)) = 0.5
        _SubsurfaceColor("Subsurface Color", Color) = (0.3, 0.8, 0.3, 1.0)
        
        [Header(Fade Settings)]
        _Fade("Top Fade Offset", Range(-1, 10)) = 1.0
        _Stretch("Top Fade Stretch", Range(0.1, 10)) = 2.0
        _Edge("Edge Fade", Range(0, 1)) = 0.1
        
        [Header(Wind)]
        _WindTint("Wind Tint", Color) = (0.9, 0.95, 0.8, 1.0)
        _WindStrengthMultiplier("Wind Strength Multiplier", Range(0, 2)) = 1.0
        
        [Header(Terrain Blending)]
        [Toggle(BLEND_TERRAIN)] _BlendTerrain("Blend with Terrain", Float) = 0
        _TerrainDiffuse("Terrain Texture", 2D) = "white" {}
        _BlendStrength("Blend Strength", Range(0, 1)) = 0.5
        
        [Header(Lighting)]
        _TranslucencyStrength("Translucency Strength", Range(0, 2)) = 1.0
        _ShadowStrength("Shadow Strength", Range(0, 1)) = 0.8
        
        [Header(Cut Grass)]
        _CutGrassColor("Cut Grass Color", Color) = (0.4, 0.3, 0.2, 1.0)
        _CutGrassHeight("Cut Grass Height", Range(0, 1)) = 0.1
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType" = "Opaque" 
            "Queue" = "Geometry"
            "RenderPipeline" = "UniversalPipeline"
        }
        
        LOD 200
        Cull Off
        ZWrite On
        ZTest LEqual
        
        Pass
        {
            Name "ForwardLit"
            Tags { "LightMode" = "UniversalForward" }
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            // Unity keywords
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE
            #pragma multi_compile _ _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS
            #pragma multi_compile _ _ADDITIONAL_LIGHT_SHADOWS
            #pragma multi_compile _ _SHADOWS_SOFT
            #pragma multi_compile _ _SCREEN_SPACE_OCCLUSION
            
            // Shader features
            #pragma shader_feature_local BLEND_TERRAIN
            
            // GPU Instancing
            #pragma multi_compile_instancing
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"
            
            // Data structures matching the compute shader
            struct DrawVertex
            {
                float3 positionWS;
                float2 uv;
            };
            
            struct DrawTriangle
            {
                float3 normalOS;
                float3 diffuseColor;
                float4 extraBuffer; // x: cut height, y-w: reserved
                DrawVertex vertices[3];
            };
            
            // Buffers
            #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                StructuredBuffer<DrawTriangle> _DrawTriangles;
            #endif
            
            // Properties
            CBUFFER_START(UnityPerMaterial)
                float4 _TopTint;
                float4 _BottomTint;
                float4 _AmbientAdjustmentColor;
                float4 _SubsurfaceColor;
                float4 _WindTint;
                float4 _CutGrassColor;
                
                float _Metallic;
                float _Smoothness;
                float _Subsurface;
                float _Fade;
                float _Stretch;
                float _Edge;
                float _WindStrengthMultiplier;
                float _BlendStrength;
                float _TranslucencyStrength;
                float _ShadowStrength;
                float _CutGrassHeight;
                
                float _OrthographicCamSizeTerrain;
                float3 _OrthographicCamPosTerrain;
            CBUFFER_END
            
            #ifdef BLEND_TERRAIN
                TEXTURE2D(_TerrainDiffuse);
                SAMPLER(sampler_TerrainDiffuse);
            #endif
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 texcoord : TEXCOORD0;
                uint vertexID : SV_VertexID;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };
            
            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float3 positionWS : TEXCOORD0;
                float3 normalWS : TEXCOORD1;
                float2 uv : TEXCOORD2;
                float3 diffuseColor : TEXCOORD3;
                float4 extraBuffer : TEXCOORD4;
                float4 shadowCoord : TEXCOORD5;
                float3 viewDirWS : TEXCOORD6;
                
                UNITY_VERTEX_INPUT_INSTANCE_ID
                UNITY_VERTEX_OUTPUT_STEREO
            };
            
            // Vertex shader
            Varyings vert(Attributes input)
            {
                Varyings output = (Varyings)0;
                
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);
                
                #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                    // Get data from compute shader buffer
                    DrawTriangle tri = _DrawTriangles[input.vertexID / 3];
                    DrawVertex vertex = tri.vertices[input.vertexID % 3];
                    
                    // Set position and normal
                    output.positionWS = vertex.positionWS;
                    output.positionCS = TransformWorldToHClip(vertex.positionWS);
                    output.normalWS = TransformObjectToWorldNormal(tri.normalOS);
                    
                    // UV and color data
                    output.uv = vertex.uv;
                    output.diffuseColor = tri.diffuseColor;
                    output.extraBuffer = tri.extraBuffer;
                    
                    // Handle cut grass special case
                    if (tri.extraBuffer.x == -1)
                    {
                        output.extraBuffer.x = 99999; // Very high value to disable cutting
                    }
                    
                    // View direction and shadows
                    output.viewDirWS = GetWorldSpaceViewDir(vertex.positionWS);
                    output.shadowCoord = TransformWorldToShadowCoord(vertex.positionWS);
                #else
                    // Fallback for unsupported platforms
                    output.positionWS = TransformObjectToWorld(input.positionOS.xyz);
                    output.positionCS = TransformWorldToHClip(output.positionWS);
                    output.normalWS = TransformObjectToWorldNormal(input.normalOS);
                    output.uv = input.texcoord;
                    output.diffuseColor = float3(0.5, 0.8, 0.3);
                    output.extraBuffer = float4(99999, 0, 0, 0);
                    output.viewDirWS = GetWorldSpaceViewDir(output.positionWS);
                    output.shadowCoord = TransformWorldToShadowCoord(output.positionWS);
                #endif
                
                return output;
            }
            
            // Fragment shader
            half4 frag(Varyings input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);
                
                // Cut grass clipping
                float cutoffTop = step(input.positionWS.y, input.extraBuffer.x);
                clip(cutoffTop - 0.01);
                
                // Normalize vectors
                float3 normalWS = normalize(input.normalWS);
                float3 viewDirWS = normalize(input.viewDirWS);
                
                // Vertical fade based on UV.y
                float verticalFade = saturate((input.uv.y + _Fade) * _Stretch);
                
                // Edge fade for grass blade tapering
                float edgeFade = saturate(abs(input.uv.x - 0.5) + _Edge) * input.uv.y;
                
                // Base color calculation
                float4 baseColor = lerp(_BottomTint, _TopTint * _AmbientAdjustmentColor, verticalFade);
                baseColor *= float4(input.diffuseColor, 1.0);
                
                // Terrain blending
                #ifdef BLEND_TERRAIN
                    float2 terrainUV = input.positionWS.xz - _OrthographicCamPosTerrain.xz;
                    terrainUV = terrainUV / (_OrthographicCamSizeTerrain * 2.0);
                    terrainUV += 0.5;
                    
                    float4 terrainColor = SAMPLE_TEXTURE2D(_TerrainDiffuse, sampler_TerrainDiffuse, terrainUV);
                    baseColor = lerp(terrainColor, terrainColor + (baseColor * _BlendStrength), verticalFade);
                #endif
                
                // Lighting setup
                Light mainLight = GetMainLight(input.shadowCoord);
                float3 lightColor = mainLight.color;
                float3 lightDir = mainLight.direction;
                float lightAttenuation = mainLight.shadowAttenuation;
                
                // Diffuse lighting with subsurface scattering
                float NdotL = dot(normalWS, lightDir);
                float subsurface = saturate(-NdotL) * _Subsurface;
                float diffuse = saturate(NdotL) + subsurface;
                
                // Translucency effect
                float3 translucency = _SubsurfaceColor.rgb * subsurface * _TranslucencyStrength;
                
                // Final color
                float3 finalColor = baseColor.rgb * lightColor * diffuse * lightAttenuation;
                finalColor += translucency * lightColor;
                
                // Add ambient lighting
                finalColor += SampleSH(normalWS) * baseColor.rgb * 0.5;
                
                // Apply shadow strength
                finalColor = lerp(finalColor, finalColor * _ShadowStrength, 1.0 - lightAttenuation);
                
                return half4(finalColor, 1.0);
            }
            
            ENDHLSL
        }
        
        // Shadow caster pass
        Pass
        {
            Name "ShadowCaster"
            Tags { "LightMode" = "ShadowCaster" }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull Off

            HLSLPROGRAM
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment

            #pragma multi_compile_instancing
            #pragma multi_compile _ _CASTING_PUNCTUAL_LIGHT_SHADOW

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/CommonMaterial.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/ShadowCasterPass.hlsl"

            // Same data structures
            struct DrawVertex
            {
                float3 positionWS;
                float2 uv;
            };

            struct DrawTriangle
            {
                float3 normalOS;
                float3 diffuseColor;
                float4 extraBuffer;
                DrawVertex vertices[3];
            };

            #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                StructuredBuffer<DrawTriangle> _DrawTriangles;
            #endif

            struct ShadowAttributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                uint vertexID : SV_VertexID;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct ShadowVaryings
            {
                float4 positionCS : SV_POSITION;
                float3 positionWS : TEXCOORD0;
                float4 extraBuffer : TEXCOORD1;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            float3 _LightDirection;
            float3 _LightPosition;

            ShadowVaryings ShadowPassVertex(ShadowAttributes input)
            {
                ShadowVaryings output = (ShadowVaryings)0;

                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);

                #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                    DrawTriangle tri = _DrawTriangles[input.vertexID / 3];
                    DrawVertex vertex = tri.vertices[input.vertexID % 3];

                    output.positionWS = vertex.positionWS;
                    output.extraBuffer = tri.extraBuffer;

                    if (tri.extraBuffer.x == -1)
                    {
                        output.extraBuffer.x = 99999;
                    }

                    float3 normalWS = TransformObjectToWorldNormal(tri.normalOS);

                    #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                        float3 lightDirectionWS = normalize(_LightPosition - vertex.positionWS);
                    #else
                        float3 lightDirectionWS = _LightDirection;
                    #endif

                    output.positionCS = TransformWorldToHClip(ApplyShadowBias(vertex.positionWS, normalWS, lightDirectionWS));
                #else
                    float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
                    float3 normalWS = TransformObjectToWorldNormal(input.normalOS);
                    output.positionWS = positionWS;
                    output.extraBuffer = float4(99999, 0, 0, 0);

                    #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                        float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                    #else
                        float3 lightDirectionWS = _LightDirection;
                    #endif

                    output.positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));
                #endif

                return output;
            }

            half4 ShadowPassFragment(ShadowVaryings input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input);

                // Cut grass clipping
                float cutoffTop = step(input.positionWS.y, input.extraBuffer.x);
                clip(cutoffTop - 0.01);

                return 0;
            }

            ENDHLSL
        }
    }
    
    Fallback "Hidden/Universal Render Pipeline/FallbackError"
}
