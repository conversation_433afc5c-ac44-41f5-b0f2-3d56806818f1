using System.Security.AccessControl;
using System.Collections;
using UnityEngine;
using Qarth;
using System;
using EventSystem = Qarth.EventSystem;
using UnityEngine.SceneManagement;

namespace GameWish.Game
{
    public class GameplayMgr : TMonoSingleton<GameplayMgr>
    {
        private PlayerInfoMgr m_PlayInfoMgr;
        private int m_GameplayTimer;
        private bool m_LoadSceneFinish = false;
        public bool loadFinish => m_LoadSceneFinish;
        private bool m_IsPlaying = false;

        private float m_BuzzLocker;
        private bool m_BuzzState = true;
        private int m_GameStartTimer = -1;
        private int m_BGMId;
        public bool buzzState
        {
            get { return m_BuzzState; }
            set { m_BuzzState = value; }
        }

        public bool isPlaying
        {
            get { return m_IsPlaying; }
            set { m_IsPlaying = value; }
        }

        private void Update()
        {
            if (m_BuzzLocker > 0)
                m_BuzzLocker -= Time.deltaTime;

            EntityMgr.S.Tick(Time.deltaTime * GameTimeMgr.S.timeScale);
        }

        public void InitGameplay()
        {
            m_PlayInfoMgr = new PlayerInfoMgr();
            StartGameplay();
        }

        void LoadRes()
        {
            InitGamePool();
            InitAudio();
        }

        void StartGameplay()
        {
            DG.Tweening.DOTween.SetTweensCapacity(400, 400);
            UnityIapMgr.S.Init();
            LoadRes();
            EventSystem.S.Register(EngineEventID.OnApplicationPauseChange, OnGamePauseChange);
            EventSystem.S.Register(EngineEventID.OnApplicationFocusChange, OnGameFocusChange);
            EventSystem.S.Register(EngineEventID.OnLanguageTableSwitchFinish, OnLanguageTableSwitchFinish);
            EventSystem.S.Register(EventID.OnHideLoading, OnHideLoading);
            AdsPlayMgr.S.Init();
            RemoteDataMgr.S.Init();
            CheckTimeReward();
            RedPointMgr.S.InitRedPointTreeNode();
            var so = SOMgr.S;
            FontMgr.S.Init();
            var iap = IapRewardMgr.S;
            Log.e("Support ComputeShader :" + SystemInfo.supportsComputeShaders);
            Log.e("GraphicsDeviceType :" + SystemInfo.graphicsDeviceType);
            Log.e("GraphicsDeviceVersion :" + SystemInfo.graphicsDeviceVersion);
            Log.e("GraphicsShaderLevel :" + SystemInfo.graphicsShaderLevel);
            Log.e("SupportsGraphicsFence :" + SystemInfo.supportsGraphicsFence);
            Log.e("MaxGraphicsBufferSize :" + SystemInfo.maxGraphicsBufferSize);

            StartCoroutine(LoadScene("GameWorld", true, () =>
            {
                UIMgr.S.OpenTopPanel(UIID.LoadingPanel, null);
                UIMgr.S.ClosePanelAsUIID(UIID.LogoPanel);
                m_LoadSceneFinish = true;
                EnergyCheck.S.Init();
                GameLogicMgr.S.Init();
                PopWindowMgr.S.Init();
                RedPointMgr.S.CheckAllRedPoint();
                UIMgr.S.OpenPanel(UIID.MainPanel);
                UIMgr.S.OpenTopPanel(UIID.BannerPanel, null);
                // UIMgr.S.OpenPanel(UIID.BottomBarPanel);
                if (!PlayerPrefs.HasKey("lifetimeOnce"))
                {
                    PlayerPrefs.SetInt("lifetimeOnce", 0);
                }
                GameCamMgr.S.AdaptCameraToScreen();
                GameWorldMgr.S.Init();
            }));

        }



        void InitAudio()
        {
            PlayBGM();
            SoundButton.defaultClickSound = AudioID.BTN;
        }

        public void PlayBGM(string name = "", float volume = 0.6f)
        {
            if (string.IsNullOrEmpty(name))
                name = AudioID.BGM;
            m_BGMId = AudioMgr.S.PlayBg(name, true);
            AudioMgr.S.SetVolume(m_BGMId, volume);
        }

        public void StopBGM()
        {
            AudioMgr.S.Stop(m_BGMId);
        }

        public void EnableJoystick(bool enable)
        {
            // if (enable)
            // {
            //     UltimateJoystick.EnableJoystick(Define.JOYSTICK_GRASS);
            // }
            // else
            // {
            //     UltimateJoystick.DisableJoystick(Define.JOYSTICK_GRASS);
            // }
        }


        protected void InitGamePool()
        {
            EffectControl.S.InitEffectControl();
        }

        public void StartTimeRecord()
        {
            OnGameTimeRecord(0);
            Timer.S.Cancel(m_GameplayTimer);
            m_GameplayTimer = Timer.S.Post2Really(OnGameTimeRecord, 60, -1);
        }

        private void OnGamePauseChange(int key, params object[] args)
        {
            bool pause = (bool)args[0];
            if (!pause)
            {
                Log.i("Game Unpause.");
                CheckTimeReward(true);
            }
        }
        private void OnGameFocusChange(int key, params object[] args)
        {
            bool focusState = (bool)args[0];
            if (focusState)
            {
                return;
            }
            PlayerInfoMgr.Save();
        }

        private void OnHideLoading(int key, params object[] param)
        {
            UIMgr.S.OpenTopPanel(UIID.UITopPanel, null);
            UIMgr.S.OpenTopPanel(UIID.TipsPanel, null);
            GameMgr.S.StartGuide();
        }

        void OnLanguageTableSwitchFinish(int key, params object[] args)
        {
            AutoTranslationImg.ReTranslationAll();
        }

        private IEnumerator LoadScene(string sceneName, bool additive, System.Action callback)
        {
            yield return new WaitForSeconds(0.1f);

            AsyncOperation oprAsync = additive ?
                SceneManager.LoadSceneAsync(sceneName, LoadSceneMode.Additive)
                : SceneManager.LoadSceneAsync(sceneName);
            oprAsync.allowSceneActivation = false;

            while (!oprAsync.isDone)//done这个变量在allowSceneActivation为true以后自然会设置
            {
                if (oprAsync.progress >= 0.9f) //unity的机制是卡在0.9,防止浮点数不准确,写0.85
                    oprAsync.allowSceneActivation = true;
                yield return new WaitForEndOfFrame();
            }

            //!!important
            SceneManager.SetActiveScene(SceneManager.GetSceneByName(sceneName));
            oprAsync = null;
            callback.Invoke();
        }


        #region OFFLINE_REWARD

        //每隔一段时间请求一下服务器时间存档作为最后游玩时间(ms时间戳)
        private void OnGameTimeRecord(int count)
        {
            PlayerInfoMgr.data.SetLastPlayTime(CustomExtensions.GetTimeStamp());
            PlayerInfoMgr.data.ResetDailyData();
            PlayerInfoMgr.Save();
        }

        void CheckTimeReward(bool isPauseCheck = false)
        {
            string lastTimeStr = PlayerInfoMgr.data.lastPlayTimeString;
            if (!string.IsNullOrEmpty(lastTimeStr) && lastTimeStr != "0")
            {
                long adds = CustomExtensions.GetSecFromTimestamps(lastTimeStr);
                Log.i(adds);

                int min = (int)(adds / 60);
                if (min >= 5)
                {
                    // CustomExtensions.CallWithDelay(this, () =>
                    // {
                    //     UIMgr.S.OpenPanel(UIID.OfflinePanel, min);
                    // }, 4);
                }
                // DateTime dtStart = new DateTime(1970, 1, 1, 0, 0, 0, 0);
                // var timeStr = CustomExtensions.GetTimeStamp();
                // //有游玩记录但是联网失败就不刷新计时也不给奖
                // if (!string.IsNullOrEmpty(timeStr))
                // {
                //     //GameData.LastPlayTimeString = timeStr;
                //     long longTimeLast = long.Parse(timeStr);
                //     long.TryParse(lastTimeStr, out longTimeLast);
                //     var dtLast = dtStart.AddMilliseconds(longTimeLast);
                //     var adds = (int)(DateTime.Now - dtLast).TotalSeconds;
                //     Log.i(adds);
                //     if (FarmGuideMgr.S.CanShowDropBox())
                //     {
                //         BuffMgr.S.ShowOfflineReward((int)(DateTime.Now - dtLast).TotalMinutes);
                //     }
                // }
            }
            if (isPauseCheck)
            {
                PlayerInfoMgr.data.SetLastPlayTime(CustomExtensions.GetTimeStamp());
            }
            else
            {
                StartTimeRecord();
            }
        }
        #endregion

        public bool CheckisOnline()
        {
            if (Application.internetReachability == NetworkReachability.NotReachable)
            {
                return false;
            }
            return true;
        }

        public void GameBuzz(long time)
        {
            if (m_BuzzState && m_BuzzLocker <= 0)
            {
                CustomExtensions.DoBuzz(time);
                m_BuzzLocker = 0.08f;
            }
        }

        public void RestartApp()
        {
#if UNITY_EDITOR
            Log.e("设置多语言重启应用");
            Application.Quit();

#elif UNITY_ANDROID
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject activity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            AndroidJavaClass restartHelper = new AndroidJavaClass("com.example.restart.RestartHelper");
            restartHelper.CallStatic("RestartApp", activity);
#endif

        }

    }

}