# Grass Blades Shaders

这些shader是专门为配合 `GrassBlades.compute` ComputeShader 而设计的草渲染着色器。

## 文件说明

### 1. GrassBlades.shader (URP版本)
- **渲染管线**: Universal Render Pipeline (URP)
- **特性**: 完整功能版本，包含所有高级特性
- **适用场景**: PC和高端移动设备

**主要特性**:
- 完整的PBR光照模型
- 次表面散射效果
- 透光效果
- 风效果支持
- 地形混合
- 草被切割支持
- 阴影投射和接收
- 多光源支持

### 2. GrassBladesBuiltIn.shader (Built-in版本)
- **渲染管线**: Built-in Render Pipeline
- **特性**: 与URP版本功能相同，但使用Built-in渲染管线
- **适用场景**: 使用Built-in渲染管线的项目

### 3. GrassBladesMobile.shader (移动端优化版本)
- **渲染管线**: Universal Render Pipeline (URP)
- **特性**: 简化版本，专为移动设备优化
- **适用场景**: 移动设备和低端硬件

**优化特性**:
- 简化的光照计算
- 移除了次表面散射
- 减少了纹理采样
- 更少的数学运算

## ComputeShader数据结构支持

所有shader都完全支持 `GrassBlades.compute` 生成的数据结构：

```hlsl
struct DrawVertex
{
    float3 positionWS; // 世界空间位置
    float2 uv;         // UV坐标
};

struct DrawTriangle
{
    float3 normalOS;      // 法线
    float3 diffuseColor;  // 漫反射颜色
    float4 extraBuffer;   // 额外数据 (x: 切割高度)
    DrawVertex vertices[3]; // 三角形顶点
};
```

## 主要功能

### 1. 草被切割系统
- 支持动态切割效果
- 通过 `extraBuffer.x` 控制切割高度
- 切割后的草根可以显示不同颜色

### 2. 风效果
- 自动从ComputeShader接收风变形数据
- 支持风强度调节
- 风色调调节

### 3. 地形混合
- 可选的地形纹理混合
- 通过 `BLEND_TERRAIN` 关键字控制
- 支持世界空间UV映射

### 4. 光照效果
- PBR材质属性 (金属度、光滑度)
- 次表面散射模拟
- 透光效果
- 阴影支持

### 5. 颜色渐变
- 顶部和底部颜色插值
- 基于UV.y的垂直渐变
- 环境光调节

## 使用方法

1. **选择合适的shader版本**:
   - URP项目使用 `GrassBlades.shader`
   - Built-in项目使用 `GrassBladesBuiltIn.shader`
   - 移动设备使用 `GrassBladesMobile.shader`

2. **创建材质**:
   - 在Unity中创建新材质
   - 选择对应的shader
   - 调整参数

3. **设置参数**:
   - **Grass Colors**: 设置草的顶部和底部颜色
   - **Material Properties**: 调整PBR属性
   - **Fade Settings**: 控制渐变效果
   - **Terrain Blending**: 如需要，启用地形混合
   - **Lighting**: 调整光照效果

4. **与ComputeShader配合**:
   - 确保材质使用的StructuredBuffer名称与ComputeShader一致
   - 设置正确的 `_DrawTriangles` 缓冲区
   - 配置地形相关参数 (如果使用地形混合)

## 性能建议

- **高端设备**: 使用完整版本 (GrassBlades.shader 或 GrassBladesBuiltIn.shader)
- **移动设备**: 使用移动优化版本 (GrassBladesMobile.shader)
- **VR设备**: 建议使用移动优化版本以保持帧率

## 兼容性

- **Unity版本**: 2019.4 LTS 及以上
- **渲染管线**: URP 7.0+ 或 Built-in
- **平台**: Windows, Mac, Linux, iOS, Android
- **图形API**: DirectX 11+, Metal, Vulkan, OpenGL ES 3.0+

## 注意事项

1. 确保项目支持ComputeShader (需要DirectX 11或更高版本)
2. 移动设备上建议限制草的密度以保持性能
3. 地形混合功能需要额外的纹理内存
4. 阴影投射会增加渲染开销，可根据需要禁用
