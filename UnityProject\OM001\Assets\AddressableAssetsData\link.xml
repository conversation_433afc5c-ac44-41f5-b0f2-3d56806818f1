<linker>
  <assembly fullname="Animancer, Version=*******, Culture=neutral, PublicKeyToken=null">
    <type fullname="Animancer.AnimancerComponent" preserve="all" />
    <type fullname="Animancer.AnimancerEvent/Sequence/Serializable" preserve="nothing" serialized="true" />
    <type fullname="Animancer.ClipTransition" preserve="nothing" serialized="true" />
    <type fullname="Animancer.LinearMixerTransition" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="DG.Tweening.DOTweenAnimation" preserve="all" />
    <type fullname="DoozyUI.UIElement" preserve="all" />
    <type fullname="GameWish.Game.BannerPanel" preserve="all" />
    <type fullname="GameWish.Game.BloodSlider" preserve="all" />
    <type fullname="GameWish.Game.BloodSliderPanel" preserve="all" />
    <type fullname="GameWish.Game.BossBloodSlider" preserve="all" />
    <type fullname="GameWish.Game.BottomBarPanel" preserve="all" />
    <type fullname="GameWish.Game.BottomBtnItem" preserve="all" />
    <type fullname="GameWish.Game.CheatPanel" preserve="all" />
    <type fullname="GameWish.Game.ConsumerLabel" preserve="all" />
    <type fullname="GameWish.Game.ConsumerLabel_Energy" preserve="all" />
    <type fullname="GameWish.Game.FloatDamageItem" preserve="all" />
    <type fullname="GameWish.Game.FloatDamagePanel" preserve="all" />
    <type fullname="GameWish.Game.FontSO" preserve="all" />
    <type fullname="GameWish.Game.GetSignRewardPanel" preserve="all" />
    <type fullname="GameWish.Game.GuideDescPanel" preserve="all" />
    <type fullname="GameWish.Game.IapConfigMgr" preserve="all" />
    <type fullname="GameWish.Game.LanguageSelectItem" preserve="all" />
    <type fullname="GameWish.Game.LanguageSelectPanel" preserve="all" />
    <type fullname="GameWish.Game.MainBgPanel" preserve="all" />
    <type fullname="GameWish.Game.MainPanel" preserve="all" />
    <type fullname="GameWish.Game.MyGuidePanel" preserve="all" />
    <type fullname="GameWish.Game.MyHighlightPanel" preserve="all" />
    <type fullname="GameWish.Game.RatePanel" preserve="all" />
    <type fullname="GameWish.Game.RoleAnimCtrl" preserve="all" />
    <type fullname="GameWish.Game.RoleAnimSO" preserve="all" />
    <type fullname="GameWish.Game.RoleCtrl" preserve="all" />
    <type fullname="GameWish.Game.SettingItem" preserve="all" />
    <type fullname="GameWish.Game.SettingPanel" preserve="all" />
    <type fullname="GameWish.Game.SignInItem" preserve="all" />
    <type fullname="GameWish.Game.SignInPanel" preserve="all" />
    <type fullname="GameWish.Game.SOMgr" preserve="all" />
    <type fullname="GameWish.Game.TipsPanel" preserve="all" />
    <type fullname="GameWish.Game.TipsPanel_PropertyItem" preserve="all" />
    <type fullname="GameWish.Game.TipsPanel_TipsHero" preserve="all" />
    <type fullname="GameWish.Game.TipsPanel_TipsItem" preserve="all" />
    <type fullname="GameWish.Game.TipsPanel_TipsLevel" preserve="all" />
    <type fullname="GameWish.Game.TipsPanel_TipsSpell" preserve="all" />
    <type fullname="GameWish.Game.TipsPanel_TipsTarot" preserve="all" />
    <type fullname="GameWish.Game.UIAnimPanel" preserve="all" />
    <type fullname="GameWish.Game.UITopPanel" preserve="all" />
    <type fullname="Qarth.AdDisplayer" preserve="all" />
    <type fullname="Qarth.AutoTranslationTMP" preserve="all" />
    <type fullname="Qarth.CircleMaskPanel" preserve="all" />
    <type fullname="Qarth.FloatMessageItem" preserve="all" />
    <type fullname="Qarth.FloatMessagePanel" preserve="all" />
    <type fullname="Qarth.MyMaskPanel" preserve="all" />
    <type fullname="Qarth.OfficialVersionAdPanel" preserve="all" />
    <type fullname="Qarth.PopButton" preserve="all" />
    <type fullname="Qarth.RedpointUI" preserve="all" />
    <type fullname="Qarth.RotateForever" preserve="all" />
    <type fullname="Qarth.SoundButton" preserve="all" />
    <type fullname="ReturnToCutPool" preserve="all" />
    <type fullname="ShaderInteractor" preserve="all" />
    <type fullname="SO_GrassSettings" preserve="all" />
    <type fullname="SO_GrassToolSettings" preserve="all" />
    <type fullname="TestCutting" preserve="all" />
    <type fullname="DoozyUI.Anim" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Fade" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.FadeLoop" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Loop" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Move" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.MoveLoop" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Rotate" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.RotateLoop" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.Scale" preserve="nothing" serialized="true" />
    <type fullname="DoozyUI.ScaleLoop" preserve="nothing" serialized="true" />
    <type fullname="GameWish.Game.FontKeyPair" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="spine-unity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Spine.Unity.SkeletonGraphic" preserve="all" />
    <type fullname="Spine.Unity.MeshGenerator" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.MeshGenerator/Settings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AtlasSpriteProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_ColorGradient" preserve="all" />
    <type fullname="TMPro.TMP_Dropdown" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.TMP_InputField" preserve="all" />
    <type fullname="TMPro.TMP_SpriteAsset" preserve="all" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_SpriteCharacter" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_SpriteGlyph" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/DropdownEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/OptionData" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/OptionDataList" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/OnChangeEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/SelectionEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/SubmitEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/TextSelectionEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/TouchScreenKeyboardEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.AvatarMask" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AudioClip" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ComputeShader" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.CharacterController" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.UI.Button" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.Scrollbar" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.Shadow" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.Toggle" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEditor.CoreModule">
    <type fullname="UnityEditor.AnimatedValues.AnimBool" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphValueRecord" preserve="nothing" serialized="true" />
  </assembly>
</linker>