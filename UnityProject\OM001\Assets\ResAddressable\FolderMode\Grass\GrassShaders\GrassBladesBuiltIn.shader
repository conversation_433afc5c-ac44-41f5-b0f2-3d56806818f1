Shader "Custom/GrassBladesBuiltIn"
{
    Properties
    {
        [Header(Grass Colors)]
        _TopTint("Top Tint", Color) = (0.8, 1.0, 0.6, 1.0)
        _BottomTint("Bottom Tint", Color) = (0.2, 0.6, 0.2, 1.0)
        _AmbientAdjustmentColor("Ambient Adjustment", Color) = (0.5, 0.5, 0.5, 1.0)
        
        [Header(Material Properties)]
        _Metallic("Metallic", Range(0, 1)) = 0.0
        _Glossiness("Smoothness", Range(0, 1)) = 0.3
        _Subsurface("Subsurface", Range(0, 1)) = 0.5
        _SubsurfaceColor("Subsurface Color", Color) = (0.3, 0.8, 0.3, 1.0)
        
        [Header(Fade Settings)]
        _Fade("Top Fade Offset", Range(-1, 10)) = 1.0
        _Stretch("Top Fade Stretch", Range(0.1, 10)) = 2.0
        _Edge("Edge Fade", Range(0, 1)) = 0.1
        
        [Header(Wind)]
        _WindTint("Wind Tint", Color) = (0.9, 0.95, 0.8, 1.0)
        _WindStrengthMultiplier("Wind Strength Multiplier", Range(0, 2)) = 1.0
        
        [Header(Terrain Blending)]
        [Toggle(BLEND_TERRAIN)] _BlendTerrain("Blend with Terrain", Float) = 0
        _TerrainDiffuse("Terrain Texture", 2D) = "white" {}
        _BlendStrength("Blend Strength", Range(0, 1)) = 0.5
        
        [Header(Lighting)]
        _TranslucencyStrength("Translucency Strength", Range(0, 2)) = 1.0
        _ShadowStrength("Shadow Strength", Range(0, 1)) = 0.8
        
        [Header(Cut Grass)]
        _CutGrassColor("Cut Grass Color", Color) = (0.4, 0.3, 0.2, 1.0)
        _CutGrassHeight("Cut Grass Height", Range(0, 1)) = 0.1
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderType" = "Opaque" 
            "Queue" = "Geometry"
            "IgnoreProjector" = "True"
        }
        
        LOD 200
        Cull Off
        
        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows vertex:vert addshadow
        #pragma target 4.5
        #pragma shader_feature BLEND_TERRAIN
        #pragma multi_compile_instancing
        
        // Data structures matching the compute shader
        struct DrawVertex
        {
            float3 positionWS;
            float2 uv;
        };
        
        struct DrawTriangle
        {
            float3 normalOS;
            float3 diffuseColor;
            float4 extraBuffer; // x: cut height, y-w: reserved
            DrawVertex vertices[3];
        };
        
        // Buffers
        #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
            StructuredBuffer<DrawTriangle> _DrawTriangles;
        #endif
        
        // Properties
        float4 _TopTint;
        float4 _BottomTint;
        float4 _AmbientAdjustmentColor;
        float4 _SubsurfaceColor;
        float4 _WindTint;
        float4 _CutGrassColor;
        
        half _Metallic;
        half _Glossiness;
        half _Subsurface;
        float _Fade;
        float _Stretch;
        half _Edge;
        float _WindStrengthMultiplier;
        float _BlendStrength;
        float _TranslucencyStrength;
        float _ShadowStrength;
        float _CutGrassHeight;
        
        float _OrthographicCamSizeTerrain;
        float3 _OrthographicCamPosTerrain;
        
        #ifdef BLEND_TERRAIN
            sampler2D _TerrainDiffuse;
        #endif
        
        struct Input
        {
            float2 uv_MainTex;
            float3 diffuseColor;
            float3 worldPos;
            float2 texcoord;
            float4 extraBuffer;
            float3 viewDir;
            float3 worldNormal;
        };
        
        struct appdata_grass
        {
            float4 vertex : POSITION;
            float3 normal : NORMAL;
            float4 tangent : TANGENT;
            float2 texcoord : TEXCOORD0;
            float2 texcoord1 : TEXCOORD1;
            float2 texcoord2 : TEXCOORD2;
            float3 color : COLOR;
            uint vertexID : SV_VertexID;
            UNITY_VERTEX_INPUT_INSTANCE_ID
        };
        
        void vert(inout appdata_grass v, out Input o)
        {
            UNITY_INITIALIZE_OUTPUT(Input, o);
            UNITY_SETUP_INSTANCE_ID(v);
            
            #if defined(SHADER_API_D3D11) || defined(SHADER_API_METAL) || defined(SHADER_API_VULKAN)
                // Get data from compute shader buffer
                DrawTriangle tri = _DrawTriangles[v.vertexID / 3];
                DrawVertex vertex = tri.vertices[v.vertexID % 3];
                
                // Transform to object space for surface shader
                v.vertex = mul(unity_WorldToObject, float4(vertex.positionWS, 1));
                v.normal = tri.normalOS;
                v.texcoord = vertex.uv;
                
                // Pass data to fragment
                o.worldPos = vertex.positionWS;
                o.uv_MainTex = vertex.uv;
                o.texcoord = vertex.uv;
                o.diffuseColor = tri.diffuseColor;
                o.extraBuffer = tri.extraBuffer;
                
                // Handle cut grass special case
                if (tri.extraBuffer.x == -1)
                {
                    o.extraBuffer.x = 99999; // Very high value to disable cutting
                }
            #else
                // Fallback for unsupported platforms
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                o.uv_MainTex = v.texcoord;
                o.texcoord = v.texcoord;
                o.diffuseColor = float3(0.5, 0.8, 0.3);
                o.extraBuffer = float4(99999, 0, 0, 0);
            #endif
        }
        
        void surf(Input IN, inout SurfaceOutputStandard o)
        {
            // Cut grass clipping
            float cutoffTop = step(IN.worldPos.y, IN.extraBuffer.x);
            clip(cutoffTop - 0.01);
            
            // Vertical fade based on UV.y
            float verticalFade = saturate((IN.texcoord.y + _Fade) * _Stretch);
            
            // Edge fade for grass blade tapering
            float edgeFade = saturate(abs(IN.texcoord.x - 0.5) + _Edge) * IN.texcoord.y;
            
            // Base color calculation
            float4 baseColor = lerp(_BottomTint, _TopTint * _AmbientAdjustmentColor, verticalFade);
            baseColor *= float4(IN.diffuseColor, 1.0);
            
            // Terrain blending
            #ifdef BLEND_TERRAIN
                float2 terrainUV = IN.worldPos.xz - _OrthographicCamPosTerrain.xz;
                terrainUV = terrainUV / (_OrthographicCamSizeTerrain * 2.0);
                terrainUV += 0.5;
                
                float4 terrainColor = tex2D(_TerrainDiffuse, terrainUV);
                baseColor = lerp(terrainColor, terrainColor + (baseColor * _BlendStrength), verticalFade);
            #endif
            
            // Subsurface scattering effect
            float3 lightDir = normalize(_WorldSpaceLightPos0.xyz);
            float3 normal = normalize(IN.worldNormal);
            float NdotL = dot(normal, lightDir);
            float subsurface = saturate(-NdotL) * _Subsurface;
            
            // Translucency
            float3 translucency = _SubsurfaceColor.rgb * subsurface * _TranslucencyStrength;
            baseColor.rgb += translucency;
            
            // Output
            o.Albedo = baseColor.rgb;
            o.Metallic = _Metallic * edgeFade;
            o.Smoothness = _Glossiness * edgeFade;
            o.Alpha = 1.0;
        }
        
        ENDCG
    }
    
    Fallback "VertexLit"
}
